#include "atlasmanager.h"

// VTK includes
#include <vtkMarchingCubes.h>
#include <vtkImageStatistics.h>
#include <vtkImageThreshold.h>
#include <vtkImageMathematics.h>

// Qt includes
#include <QDebug>
#include <QFileInfo>
#include <QJsonArray>
#include <QFile>
#include <QTextStream>
#include <QRegularExpression>

AtlasManager::AtlasManager(QObject *parent)
    : QObject(parent)
    , m_atlasName("Unknown Atlas")
    , m_atlasDescription("Brain atlas for region annotation")
    , m_atlasVersion("1.0")
{
    setupStandardRegions();
}

AtlasManager::~AtlasManager()
{
}

bool AtlasManager::loadAtlasRegions(const QString &atlasFileName)
{
    QFileInfo fileInfo(atlasFileName);
    if (!fileInfo.exists()) {
        qWarning() << "Atlas file does not exist:" << atlasFileName;
        return false;
    }

    m_atlasFileName = atlasFileName;
    
    // Try to determine atlas type from filename
    QString baseName = fileInfo.baseName().toLower();
    
    if (baseName.contains("freesurfer") || baseName.contains("aparc")) {
        return parseFreeSurferAtlas(atlasFileName);
    } else if (baseName.contains("aal")) {
        return parseAALAtlas(atlasFileName);
    } else if (baseName.contains("brodmann")) {
        return parseBrodmannAtlas(atlasFileName);
    } else {
        return parseCustomAtlas(atlasFileName);
    }
}

bool AtlasManager::loadRegionDefinitions(const QString &definitionFile)
{
    QFile file(definitionFile);
    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "Cannot open region definition file:" << definitionFile;
        return false;
    }

    QJsonDocument doc = QJsonDocument::fromJson(file.readAll());
    if (doc.isNull()) {
        qWarning() << "Invalid JSON in region definition file";
        return false;
    }

    QJsonObject root = doc.object();
    
    // Load atlas metadata
    m_atlasName = root["name"].toString("Unknown Atlas");
    m_atlasDescription = root["description"].toString();
    m_atlasVersion = root["version"].toString("1.0");
    
    // Load regions
    QJsonArray regions = root["regions"].toArray();
    m_regions.clear();
    m_idToName.clear();
    m_categories.clear();
    
    for (const QJsonValue &value : regions) {
        QJsonObject regionObj = value.toObject();
        
        BrainRegion region;
        region.id = regionObj["id"].toInt();
        region.name = regionObj["name"].toString();
        region.fullName = regionObj["fullName"].toString();
        region.category = regionObj["category"].toString();
        region.description = regionObj["description"].toString();
        region.visible = regionObj["visible"].toBool(true);
        region.volume = regionObj["volume"].toDouble(0.0);
        
        // Parse color
        QJsonArray colorArray = regionObj["color"].toArray();
        if (colorArray.size() >= 3) {
            region.color = QColor(colorArray[0].toInt(), 
                                 colorArray[1].toInt(), 
                                 colorArray[2].toInt());
        } else {
            region.color = generateDistinctColor(region.id);
        }
        
        // Parse synonyms
        QJsonArray synonymsArray = regionObj["synonyms"].toArray();
        for (const QJsonValue &synonym : synonymsArray) {
            region.synonyms.append(synonym.toString());
        }
        
        m_regions[region.name] = region;
        m_idToName[region.id] = region.name;
        
        if (!m_categories.contains(region.category)) {
            m_categories[region.category] = QStringList();
        }
        m_categories[region.category].append(region.name);
    }
    
    emit atlasLoaded(m_atlasName);
    return true;
}

bool AtlasManager::saveRegionDefinitions(const QString &definitionFile)
{
    QJsonObject root;
    root["name"] = m_atlasName;
    root["description"] = m_atlasDescription;
    root["version"] = m_atlasVersion;
    
    QJsonArray regionsArray;
    for (auto it = m_regions.begin(); it != m_regions.end(); ++it) {
        const BrainRegion &region = it.value();
        
        QJsonObject regionObj;
        regionObj["id"] = region.id;
        regionObj["name"] = region.name;
        regionObj["fullName"] = region.fullName;
        regionObj["category"] = region.category;
        regionObj["description"] = region.description;
        regionObj["visible"] = region.visible;
        regionObj["volume"] = region.volume;
        
        QJsonArray colorArray;
        colorArray.append(region.color.red());
        colorArray.append(region.color.green());
        colorArray.append(region.color.blue());
        regionObj["color"] = colorArray;
        
        QJsonArray synonymsArray;
        for (const QString &synonym : region.synonyms) {
            synonymsArray.append(synonym);
        }
        regionObj["synonyms"] = synonymsArray;
        
        regionsArray.append(regionObj);
    }
    root["regions"] = regionsArray;
    
    QJsonDocument doc(root);
    
    QFile file(definitionFile);
    if (!file.open(QIODevice::WriteOnly)) {
        qWarning() << "Cannot write to region definition file:" << definitionFile;
        return false;
    }
    
    file.write(doc.toJson());
    return true;
}

QStringList AtlasManager::getRegionNames() const
{
    return m_regions.keys();
}

QStringList AtlasManager::getRegionCategories() const
{
    return m_categories.keys();
}

QStringList AtlasManager::getRegionsInCategory(const QString &category) const
{
    return m_categories.value(category, QStringList());
}

BrainRegion AtlasManager::getRegion(const QString &name) const
{
    return m_regions.value(name, BrainRegion());
}

BrainRegion AtlasManager::getRegion(int id) const
{
    QString name = m_idToName.value(id);
    return getRegion(name);
}

void AtlasManager::setRegionColor(const QString &regionName, const QColor &color)
{
    if (m_regions.contains(regionName)) {
        m_regions[regionName].color = color;
        emit regionUpdated(regionName);
    }
}

void AtlasManager::setRegionVisibility(const QString &regionName, bool visible)
{
    if (m_regions.contains(regionName)) {
        m_regions[regionName].visible = visible;
        emit regionUpdated(regionName);
    }
}

void AtlasManager::setRegionDescription(const QString &regionName, const QString &description)
{
    if (m_regions.contains(regionName)) {
        m_regions[regionName].description = description;
        emit regionUpdated(regionName);
    }
}

double AtlasManager::calculateRegionVolume(const QString &regionName, vtkSmartPointer<vtkImageData> image)
{
    if (!m_regions.contains(regionName) || !image) {
        return 0.0;
    }

    int regionId = m_regions[regionName].id;
    
    // Create threshold filter for this region
    vtkSmartPointer<vtkImageThreshold> threshold = vtkSmartPointer<vtkImageThreshold>::New();
    threshold->SetInputData(image);
    threshold->ThresholdBetween(regionId - 0.5, regionId + 0.5);
    threshold->SetInValue(1);
    threshold->SetOutValue(0);
    threshold->Update();
    
    // Calculate statistics
    vtkSmartPointer<vtkImageStatistics> stats = vtkSmartPointer<vtkImageStatistics>::New();
    stats->SetInputConnection(threshold->GetOutputPort());
    stats->Update();
    
    double voxelVolume = image->GetSpacing()[0] * image->GetSpacing()[1] * image->GetSpacing()[2];
    double volume = stats->GetSum() * voxelVolume;
    
    // Update stored volume
    m_regions[regionName].volume = volume;
    
    return volume;
}

QString AtlasManager::getRegionNameFromId(int id) const
{
    return m_idToName.value(id, "Unknown");
}

int AtlasManager::getRegionIdFromName(const QString &name) const
{
    if (m_regions.contains(name)) {
        return m_regions[name].id;
    }
    return 0;
}

QString AtlasManager::getRegionNameFromPosition(double x, double y, double z, 
                                               vtkSmartPointer<vtkImageData> atlasImage) const
{
    if (!atlasImage) {
        return "Unknown";
    }

    // Convert world coordinates to image coordinates
    int ijk[3];
    double xyz[3] = {x, y, z};
    
    // Simple coordinate conversion (assumes identity transform)
    double spacing[3];
    double origin[3];
    atlasImage->GetSpacing(spacing);
    atlasImage->GetOrigin(origin);
    
    ijk[0] = static_cast<int>((xyz[0] - origin[0]) / spacing[0]);
    ijk[1] = static_cast<int>((xyz[1] - origin[1]) / spacing[1]);
    ijk[2] = static_cast<int>((xyz[2] - origin[2]) / spacing[2]);
    
    // Check bounds
    int extent[6];
    atlasImage->GetExtent(extent);
    if (ijk[0] < extent[0] || ijk[0] > extent[1] ||
        ijk[1] < extent[2] || ijk[1] > extent[3] ||
        ijk[2] < extent[4] || ijk[2] > extent[5]) {
        return "Outside";
    }
    
    // Get voxel value
    int regionId = static_cast<int>(atlasImage->GetScalarComponentAsDouble(ijk[0], ijk[1], ijk[2], 0));
    
    return getRegionNameFromId(regionId);
}

void AtlasManager::setupStandardRegions()
{
    // Initialize with standard brain regions
    struct StandardRegion {
        int id;
        QString name;
        QString fullName;
        QString category;
        QColor color;
    };
    
    QList<StandardRegion> standardRegions = {
        {1, "Frontal_L", "Left Frontal Lobe", "Cerebral Cortex", QColor(255, 100, 100)},
        {2, "Frontal_R", "Right Frontal Lobe", "Cerebral Cortex", QColor(255, 120, 120)},
        {3, "Parietal_L", "Left Parietal Lobe", "Cerebral Cortex", QColor(100, 255, 100)},
        {4, "Parietal_R", "Right Parietal Lobe", "Cerebral Cortex", QColor(120, 255, 120)},
        {5, "Temporal_L", "Left Temporal Lobe", "Cerebral Cortex", QColor(100, 100, 255)},
        {6, "Temporal_R", "Right Temporal Lobe", "Cerebral Cortex", QColor(120, 120, 255)},
        {7, "Occipital_L", "Left Occipital Lobe", "Cerebral Cortex", QColor(255, 255, 100)},
        {8, "Occipital_R", "Right Occipital Lobe", "Cerebral Cortex", QColor(255, 255, 120)},
        {9, "Cerebellum_L", "Left Cerebellum", "Cerebellum", QColor(255, 100, 255)},
        {10, "Cerebellum_R", "Right Cerebellum", "Cerebellum", QColor(255, 120, 255)},
        {11, "Brainstem", "Brain Stem", "Brainstem", QColor(100, 255, 255)},
        {12, "Hippocampus_L", "Left Hippocampus", "Limbic System", QColor(255, 150, 50)},
        {13, "Hippocampus_R", "Right Hippocampus", "Limbic System", QColor(255, 170, 70)},
        {14, "Amygdala_L", "Left Amygdala", "Limbic System", QColor(150, 50, 255)},
        {15, "Amygdala_R", "Right Amygdala", "Limbic System", QColor(170, 70, 255)},
        {16, "Thalamus_L", "Left Thalamus", "Subcortical", QColor(50, 255, 150)},
        {17, "Thalamus_R", "Right Thalamus", "Subcortical", QColor(70, 255, 170)},
        {18, "Caudate_L", "Left Caudate", "Basal Ganglia", QColor(255, 50, 150)},
        {19, "Caudate_R", "Right Caudate", "Basal Ganglia", QColor(255, 70, 170)},
        {20, "Putamen_L", "Left Putamen", "Basal Ganglia", QColor(150, 255, 50)},
        {21, "Putamen_R", "Right Putamen", "Basal Ganglia", QColor(170, 255, 70)}
    };
    
    m_regions.clear();
    m_idToName.clear();
    m_categories.clear();
    
    for (const StandardRegion &stdRegion : standardRegions) {
        BrainRegion region;
        region.id = stdRegion.id;
        region.name = stdRegion.name;
        region.fullName = stdRegion.fullName;
        region.category = stdRegion.category;
        region.color = stdRegion.color;
        region.visible = true;
        region.volume = 0.0;
        
        m_regions[region.name] = region;
        m_idToName[region.id] = region.name;
        
        if (!m_categories.contains(region.category)) {
            m_categories[region.category] = QStringList();
        }
        m_categories[region.category].append(region.name);
    }
}

QColor AtlasManager::generateDistinctColor(int index)
{
    // Generate distinct colors using golden angle
    double hue = (index * 137.5) / 360.0;
    double saturation = 0.8;
    double value = 0.9;
    
    // HSV to RGB conversion
    int h_i = static_cast<int>(hue * 6);
    double f = hue * 6 - h_i;
    double p = value * (1 - saturation);
    double q = value * (1 - f * saturation);
    double t = value * (1 - (1 - f) * saturation);
    
    double r, g, b;
    switch (h_i % 6) {
        case 0: r = value; g = t; b = p; break;
        case 1: r = q; g = value; b = p; break;
        case 2: r = p; g = value; b = t; break;
        case 3: r = p; g = q; b = value; break;
        case 4: r = t; g = p; b = value; break;
        case 5: r = value; g = p; b = q; break;
        default: r = g = b = 0; break;
    }
    
    return QColor(static_cast<int>(r * 255), 
                  static_cast<int>(g * 255), 
                  static_cast<int>(b * 255));
}

bool AtlasManager::parseFreeSurferAtlas(const QString &fileName)
{
    // Simplified FreeSurfer atlas parsing
    m_atlasName = "FreeSurfer Atlas";
    m_atlasDescription = "FreeSurfer cortical and subcortical segmentation";
    return true;
}

bool AtlasManager::parseAALAtlas(const QString &fileName)
{
    // Simplified AAL atlas parsing
    m_atlasName = "AAL Atlas";
    m_atlasDescription = "Automated Anatomical Labeling atlas";
    return true;
}

bool AtlasManager::parseBrodmannAtlas(const QString &fileName)
{
    // Simplified Brodmann atlas parsing
    m_atlasName = "Brodmann Atlas";
    m_atlasDescription = "Brodmann areas cytoarchitectonic atlas";
    return true;
}

bool AtlasManager::parseCustomAtlas(const QString &fileName)
{
    // Generic atlas parsing
    m_atlasName = "Custom Atlas";
    m_atlasDescription = "Custom brain atlas";
    return true;
}
