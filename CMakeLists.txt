cmake_minimum_required(VERSION 3.16)
project(BrainViewer3D VERSION 1.0.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find required packages
find_package(Qt6 REQUIRED COMPONENTS Core Widgets OpenGL OpenGLWidgets)
find_package(VTK REQUIRED COMPONENTS
    CommonCore
    CommonDataModel
    CommonExecutionModel
    FiltersCore
    FiltersGeneral
    FiltersGeometry
    FiltersSources
    IOImage
    IOLegacy
    IONIFTI
    ImagingCore
    ImagingGeneral
    InteractionStyle
    InteractionWidgets
    RenderingCore
    RenderingOpenGL2
    RenderingVolume
    RenderingVolumeOpenGL2
    GUISupportQt
)

# Include VTK
include(${VTK_USE_FILE})

# Set Qt MOC, UIC, RCC
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# Source files
set(SOURCES
    main.cpp
    mainwindow.cpp
    brainviewer.cpp
    registrationmanager.cpp
    atlasmanager.cpp
)

# Header files
set(HEADERS
    mainwindow.h
    brainviewer.h
    registrationmanager.h
    atlasmanager.h
)

# Create executable
add_executable(BrainViewer3D ${SOURCES} ${HEADERS})

# Link libraries
target_link_libraries(BrainViewer3D
    Qt6::Core
    Qt6::Widgets
    Qt6::OpenGL
    Qt6::OpenGLWidgets
    ${VTK_LIBRARIES}
)

# VTK module initialization
vtk_module_autoinit(
    TARGETS BrainViewer3D
    MODULES ${VTK_LIBRARIES}
)

# Set output directory
set_target_properties(BrainViewer3D PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Install target
install(TARGETS BrainViewer3D
    RUNTIME DESTINATION bin
)
