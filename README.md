# Brain Viewer 3D - T1 MRI 可视化应用程序

一个基于Qt6和VTK的3D T1 MRI脑部图像可视化应用程序，支持标准空间配准和脑区标注功能。

## 功能特性

### 🧠 3D T1 MRI 可视化
- **体绘制 (Volume Rendering)**: 高质量的3D体积渲染
- **表面绘制 (Surface Rendering)**: 基于Marching Cubes算法的表面网格生成
- **多角度观察**: 支持轴位、矢状位、冠状位等标准视角
- **交互式相机控制**: 鼠标拖拽旋转、缩放、平移

### 🎯 脑区标注与可视化
- **颜色编码**: 不同脑区使用不同颜色进行区分
- **交互式选择**: 点击3D模型选择脑区
- **脑区树状列表**: 按类别组织的脑区层次结构
- **实时高亮**: 选中脑区的实时高亮显示

### 🔄 图像配准
- **标准空间配准**: 将标准脑图谱配准到个体T1图像
- **多种配准算法**: 支持刚体、仿射和非线性配准
- **进度监控**: 实时显示配准进度
- **配准结果可视化**: 配准后的图谱叠加显示

### 🎛️ 可视化控制
- **透明度调节**: 实时调整T1图像和脑图谱的透明度
- **阈值控制**: 表面渲染的阈值调节
- **线框模式**: 切换表面/线框显示模式
- **渲染模式切换**: 体绘制与表面绘制之间切换

## 系统要求

### 必需依赖
- **Qt6** (6.2或更高版本)
  - Qt6::Core
  - Qt6::Widgets
  - Qt6::OpenGL
  - Qt6::OpenGLWidgets
- **VTK** (9.0或更高版本)
  - 支持OpenGL2渲染后端
  - NIFTI图像读取支持
  - 体绘制模块
- **CMake** (3.16或更高版本)
- **C++17** 兼容编译器

### 支持的图像格式
- NIFTI (.nii, .nii.gz)
- DICOM (.dcm)
- MetaImage (.mhd, .mha)

## 构建说明

### 1. 克隆项目
```bash
git clone <repository-url>
cd qt_ni
```

### 2. 创建构建目录
```bash
mkdir build
cd build
```

### 3. 配置CMake
```bash
cmake .. -DCMAKE_BUILD_TYPE=Release
```

### 4. 编译
```bash
cmake --build . --config Release
```

### 5. 运行
```bash
./bin/BrainViewer3D
```

## 使用指南

### 基本工作流程

1. **加载T1图像**
   - 点击 "Load T1 Image" 按钮
   - 选择T1 MRI NIFTI文件
   - 图像将自动显示在3D视窗中

2. **加载脑图谱**
   - 点击 "Load Atlas Template" 按钮
   - 选择标准脑图谱文件
   - 脑区列表将自动填充

3. **执行配准**
   - 确保T1图像和图谱都已加载
   - 点击 "Start Registration" 按钮
   - 等待配准完成

4. **脑区可视化**
   - 启用 "Show Atlas Overlay" 复选框
   - 在脑区树中选择感兴趣的区域
   - 调整图谱透明度以获得最佳视觉效果

### 界面说明

#### 可视化选项卡
- **透明度滑块**: 调整T1图像透明度
- **阈值控制**: 设置表面渲染阈值
- **渲染模式**: 选择体绘制或表面绘制
- **视角按钮**: 快速切换到标准解剖视角

#### 配准选项卡
- **图像加载**: 加载T1和图谱图像
- **配准控制**: 启动和监控配准过程
- **进度显示**: 实时配准进度

#### 图谱选项卡
- **图谱可视化**: 控制图谱显示和透明度
- **脑区树**: 按类别浏览脑区
- **选中区域**: 管理当前选中的脑区

#### 分析选项卡
- **体积计算**: 计算各脑区体积
- **数据导出**: 导出分析结果

### 快捷键
- **Ctrl+O**: 打开T1图像
- **Ctrl+S**: 保存截图
- **Ctrl+Q**: 退出应用程序

## 技术架构

### 核心组件
- **BrainViewer**: VTK渲染窗口，负责3D可视化
- **RegistrationManager**: 图像配准管理器
- **AtlasManager**: 脑图谱和脑区管理器
- **MainWindow**: 主界面和用户交互

### 关键技术
- **VTK渲染管线**: 高性能3D图形渲染
- **Qt信号槽机制**: 组件间通信
- **多线程配准**: 避免界面冻结
- **智能指针管理**: 自动内存管理

## 开发说明

### 添加新的脑图谱
1. 在 `AtlasManager` 中添加新的解析函数
2. 更新 `loadAtlasRegions()` 方法
3. 定义脑区颜色和分类

### 扩展配准算法
1. 在 `RegistrationManager` 中实现新算法
2. 更新 `RegistrationWorker` 类
3. 添加参数配置界面

### 自定义可视化
1. 修改 `BrainViewer` 的渲染管线
2. 添加新的VTK滤波器
3. 更新用户界面控件

## 故障排除

### 常见问题
1. **VTK模块未找到**: 确保VTK正确安装并设置环境变量
2. **OpenGL错误**: 更新显卡驱动程序
3. **图像加载失败**: 检查文件格式和路径
4. **配准失败**: 确保图像尺寸和方向正确

### 调试模式
```bash
cmake .. -DCMAKE_BUILD_TYPE=Debug
```

## 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。

## 贡献

欢迎提交问题报告和功能请求。请遵循以下步骤：

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 联系方式

如有问题或建议，请联系开发团队。
