#include "mainwindow.h"
#include "brainviewer.h"
#include "registrationmanager.h"
#include "atlasmanager.h"

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_centralWidget(nullptr)
    , m_brainViewer(nullptr)
    , m_registrationManager(nullptr)
    , m_atlasManager(nullptr)
{
    setWindowTitle("Brain Viewer 3D - T1 MRI Visualization and Atlas Registration");
    setMinimumSize(1200, 800);
    resize(1600, 1000);

    // Initialize managers
    m_registrationManager = new RegistrationManager(this);
    m_atlasManager = new AtlasManager(this);

    setupUI();
    setupMenuBar();
    setupToolBar();
    setupStatusBar();
    connectSignals();

    updateStatusMessage("Ready - Load T1 image to begin");
}

MainWindow::~MainWindow()
{
}

void MainWindow::setupUI()
{
    m_centralWidget = new QWidget;
    setCentralWidget(m_centralWidget);

    m_mainLayout = new QHBoxLayout(m_centralWidget);
    m_mainSplitter = new QSplitter(Qt::Horizontal);
    m_mainLayout->addWidget(m_mainSplitter);

    // Create brain viewer
    m_brainViewer = new BrainViewer;
    m_mainSplitter->addWidget(m_brainViewer);

    // Create control tabs
    m_controlTabs = new QTabWidget;
    m_controlTabs->setMaximumWidth(400);
    m_controlTabs->setMinimumWidth(350);
    m_mainSplitter->addWidget(m_controlTabs);

    createVisualizationTab();
    createRegistrationTab();
    createAtlasTab();
    createAnalysisTab();

    // Set splitter proportions
    m_mainSplitter->setSizes({1000, 400});
}

void MainWindow::createVisualizationTab()
{
    m_visualizationTab = new QWidget;
    m_controlTabs->addTab(m_visualizationTab, "Visualization");

    QVBoxLayout *vizLayout = new QVBoxLayout(m_visualizationTab);

    // Rendering controls
    m_renderingGroup = new QGroupBox("Rendering Controls");
    QVBoxLayout *renderingLayout = new QVBoxLayout(m_renderingGroup);

    // Opacity control
    QHBoxLayout *opacityLayout = new QHBoxLayout;
    opacityLayout->addWidget(new QLabel("Opacity:"));
    m_opacitySlider = new QSlider(Qt::Horizontal);
    m_opacitySlider->setRange(0, 100);
    m_opacitySlider->setValue(100);
    opacityLayout->addWidget(m_opacitySlider);
    renderingLayout->addLayout(opacityLayout);

    // Threshold control
    QHBoxLayout *thresholdLayout = new QHBoxLayout;
    thresholdLayout->addWidget(new QLabel("Threshold:"));
    m_thresholdSpinBox = new QDoubleSpinBox;
    m_thresholdSpinBox->setRange(0.0, 255.0);
    m_thresholdSpinBox->setValue(100.0);
    m_thresholdSpinBox->setSingleStep(1.0);
    thresholdLayout->addWidget(m_thresholdSpinBox);
    renderingLayout->addLayout(thresholdLayout);

    // Rendering mode
    m_wireframeCheckBox = new QCheckBox("Wireframe Mode");
    m_volumeRenderingCheckBox = new QCheckBox("Volume Rendering");
    m_volumeRenderingCheckBox->setChecked(true);
    renderingLayout->addWidget(m_wireframeCheckBox);
    renderingLayout->addWidget(m_volumeRenderingCheckBox);

    // Rendering mode combo
    QHBoxLayout *modeLayout = new QHBoxLayout;
    modeLayout->addWidget(new QLabel("Mode:"));
    m_renderingModeCombo = new QComboBox;
    m_renderingModeCombo->addItems({"Volume Rendering", "Surface Rendering", "Both"});
    modeLayout->addWidget(m_renderingModeCombo);
    renderingLayout->addLayout(modeLayout);

    vizLayout->addWidget(m_renderingGroup);

    // View controls
    m_viewGroup = new QGroupBox("View Controls");
    QVBoxLayout *viewLayout = new QVBoxLayout(m_viewGroup);

    m_resetViewButton = new QPushButton("Reset View");
    m_axialViewButton = new QPushButton("Axial View");
    m_sagittalViewButton = new QPushButton("Sagittal View");
    m_coronalViewButton = new QPushButton("Coronal View");

    viewLayout->addWidget(m_resetViewButton);
    viewLayout->addWidget(m_axialViewButton);
    viewLayout->addWidget(m_sagittalViewButton);
    viewLayout->addWidget(m_coronalViewButton);

    vizLayout->addWidget(m_viewGroup);
    vizLayout->addStretch();
}

void MainWindow::createRegistrationTab()
{
    m_registrationTab = new QWidget;
    m_controlTabs->addTab(m_registrationTab, "Registration");

    QVBoxLayout *regLayout = new QVBoxLayout(m_registrationTab);

    m_registrationGroup = new QGroupBox("Image Registration");
    QVBoxLayout *regGroupLayout = new QVBoxLayout(m_registrationGroup);

    m_loadT1Button = new QPushButton("Load T1 Image");
    m_loadAtlasButton = new QPushButton("Load Atlas Template");
    m_startRegistrationButton = new QPushButton("Start Registration");
    m_startRegistrationButton->setEnabled(false);

    m_registrationProgress = new QProgressBar;
    m_registrationStatus = new QLabel("No images loaded");

    regGroupLayout->addWidget(m_loadT1Button);
    regGroupLayout->addWidget(m_loadAtlasButton);
    regGroupLayout->addWidget(m_startRegistrationButton);
    regGroupLayout->addWidget(m_registrationProgress);
    regGroupLayout->addWidget(m_registrationStatus);

    regLayout->addWidget(m_registrationGroup);
    regLayout->addStretch();
}

void MainWindow::createAtlasTab()
{
    m_atlasTab = new QWidget;
    m_controlTabs->addTab(m_atlasTab, "Atlas");

    QVBoxLayout *atlasLayout = new QVBoxLayout(m_atlasTab);

    m_atlasGroup = new QGroupBox("Brain Atlas");
    QVBoxLayout *atlasGroupLayout = new QVBoxLayout(m_atlasGroup);

    // Atlas visibility
    m_showAtlasCheckBox = new QCheckBox("Show Atlas Overlay");
    m_showAtlasCheckBox->setEnabled(false);
    atlasGroupLayout->addWidget(m_showAtlasCheckBox);

    // Atlas opacity
    QHBoxLayout *atlasOpacityLayout = new QHBoxLayout;
    atlasOpacityLayout->addWidget(new QLabel("Atlas Opacity:"));
    m_atlasOpacitySlider = new QSlider(Qt::Horizontal);
    m_atlasOpacitySlider->setRange(0, 100);
    m_atlasOpacitySlider->setValue(60);
    atlasOpacityLayout->addWidget(m_atlasOpacitySlider);
    atlasGroupLayout->addLayout(atlasOpacityLayout);

    // Brain regions tree
    atlasGroupLayout->addWidget(new QLabel("Brain Regions:"));
    m_brainRegionTree = new QTreeWidget;
    m_brainRegionTree->setHeaderLabel("Region");
    m_brainRegionTree->setEnabled(false);
    atlasGroupLayout->addWidget(m_brainRegionTree);

    // Selected regions
    atlasGroupLayout->addWidget(new QLabel("Selected Regions:"));
    m_selectedRegionsList = new QListWidget;
    m_clearSelectionButton = new QPushButton("Clear Selection");
    atlasGroupLayout->addWidget(m_selectedRegionsList);
    atlasGroupLayout->addWidget(m_clearSelectionButton);

    atlasLayout->addWidget(m_atlasGroup);
}

void MainWindow::createAnalysisTab()
{
    m_analysisTab = new QWidget;
    m_controlTabs->addTab(m_analysisTab, "Analysis");

    QVBoxLayout *analysisLayout = new QVBoxLayout(m_analysisTab);

    m_analysisGroup = new QGroupBox("Region Analysis");
    QVBoxLayout *analysisGroupLayout = new QVBoxLayout(m_analysisGroup);

    m_calculateVolumeButton = new QPushButton("Calculate Volumes");
    m_exportDataButton = new QPushButton("Export Data");

    m_analysisResults = new QTextEdit;
    m_analysisResults->setReadOnly(true);
    m_analysisResults->setMaximumHeight(200);

    analysisGroupLayout->addWidget(m_calculateVolumeButton);
    analysisGroupLayout->addWidget(m_exportDataButton);
    analysisGroupLayout->addWidget(new QLabel("Results:"));
    analysisGroupLayout->addWidget(m_analysisResults);

    analysisLayout->addWidget(m_analysisGroup);
    analysisLayout->addStretch();
}

void MainWindow::setupMenuBar()
{
    m_menuBar = menuBar();

    // File menu
    m_fileMenu = m_menuBar->addMenu("&File");
    m_openT1Action = m_fileMenu->addAction("Open &T1 Image...");
    m_openT1Action->setShortcut(QKeySequence::Open);
    m_openAtlasAction = m_fileMenu->addAction("Open &Atlas...");
    m_fileMenu->addSeparator();
    m_saveScreenshotAction = m_fileMenu->addAction("Save &Screenshot...");
    m_saveScreenshotAction->setShortcut(QKeySequence("Ctrl+S"));
    m_fileMenu->addSeparator();
    m_exitAction = m_fileMenu->addAction("E&xit");
    m_exitAction->setShortcut(QKeySequence::Quit);

    // View menu
    m_viewMenu = m_menuBar->addMenu("&View");
    m_viewMenu->addAction("Reset View", this, &MainWindow::resetView);
    m_viewMenu->addSeparator();
    m_viewMenu->addAction("Axial View", this, &MainWindow::setAxialView);
    m_viewMenu->addAction("Sagittal View", this, &MainWindow::setSagittalView);
    m_viewMenu->addAction("Coronal View", this, &MainWindow::setCoronalView);

    // Tools menu
    m_toolsMenu = m_menuBar->addMenu("&Tools");
    m_toolsMenu->addAction("Start Registration", m_registrationManager, &RegistrationManager::startRegistration);

    // Help menu
    m_helpMenu = m_menuBar->addMenu("&Help");
    m_aboutAction = m_helpMenu->addAction("&About");
}

void MainWindow::setupToolBar()
{
    m_mainToolBar = addToolBar("Main");
    m_mainToolBar->addAction(m_openT1Action);
    m_mainToolBar->addAction(m_openAtlasAction);
    m_mainToolBar->addSeparator();
    m_mainToolBar->addAction(m_saveScreenshotAction);
}

void MainWindow::setupStatusBar()
{
    m_statusBar = statusBar();
    m_statusLabel = new QLabel("Ready");
    m_statusProgress = new QProgressBar;
    m_statusProgress->setVisible(false);
    m_statusProgress->setMaximumWidth(200);

    m_statusBar->addWidget(m_statusLabel, 1);
    m_statusBar->addPermanentWidget(m_statusProgress);
}

void MainWindow::connectSignals()
{
    // Brain viewer signals
    connect(m_brainViewer, &BrainViewer::imageLoaded,
            [this](const QString &fileName) {
                updateStatusMessage("Image loaded: " + QFileInfo(fileName).baseName());
            });
    connect(m_brainViewer, &BrainViewer::brainRegionClicked,
            this, &MainWindow::onBrainRegionClicked);

    // Visualization controls
    connect(m_opacitySlider, &QSlider::valueChanged, this, &MainWindow::updateOpacity);
    connect(m_thresholdSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &MainWindow::updateThreshold);
    connect(m_wireframeCheckBox, &QCheckBox::toggled, this, &MainWindow::toggleWireframe);
    connect(m_volumeRenderingCheckBox, &QCheckBox::toggled, this, &MainWindow::toggleVolumeRendering);

    // View controls
    connect(m_resetViewButton, &QPushButton::clicked, this, &MainWindow::resetView);
    connect(m_axialViewButton, &QPushButton::clicked, this, &MainWindow::setAxialView);
    connect(m_sagittalViewButton, &QPushButton::clicked, this, &MainWindow::setSagittalView);
    connect(m_coronalViewButton, &QPushButton::clicked, this, &MainWindow::setCoronalView);

    // Registration controls
    connect(m_loadT1Button, &QPushButton::clicked, this, &MainWindow::openT1Image);
    connect(m_loadAtlasButton, &QPushButton::clicked, this, &MainWindow::openAtlasImage);
    connect(m_startRegistrationButton, &QPushButton::clicked,
            m_registrationManager, &RegistrationManager::startRegistration);

    // Atlas controls
    connect(m_showAtlasCheckBox, &QCheckBox::toggled, this, &MainWindow::toggleAtlasVisibility);
    connect(m_atlasOpacitySlider, &QSlider::valueChanged, this, &MainWindow::updateAtlasOpacity);
    connect(m_brainRegionTree, &QTreeWidget::itemClicked,
            [this](QTreeWidgetItem *item) {
                if (item) {
                    onBrainRegionSelected(item->text(0));
                }
            });
    connect(m_clearSelectionButton, &QPushButton::clicked, this, &MainWindow::clearRegionSelection);

    // Manager signals
    connect(m_registrationManager, &RegistrationManager::progressUpdated,
            this, &MainWindow::onRegistrationProgress);
    connect(m_registrationManager, &RegistrationManager::registrationCompleted,
            this, &MainWindow::onRegistrationCompleted);

    // Menu actions
    connect(m_openT1Action, &QAction::triggered, this, &MainWindow::openT1Image);
    connect(m_openAtlasAction, &QAction::triggered, this, &MainWindow::openAtlasImage);
    connect(m_saveScreenshotAction, &QAction::triggered, this, &MainWindow::saveScreenshot);
    connect(m_exitAction, &QAction::triggered, this, &QWidget::close);
    connect(m_aboutAction, &QAction::triggered, [this]() {
        QMessageBox::about(this, "About Brain Viewer 3D",
            "Brain Viewer 3D v1.0\n\n"
            "A Qt application for 3D visualization of T1 MRI images\n"
            "with atlas registration and brain region annotation.\n\n"
            "Built with Qt6 and VTK.");
    });
}

// Slot implementations
void MainWindow::openT1Image()
{
    QString fileName = QFileDialog::getOpenFileName(this,
        "Open T1 MRI Image", "",
        "Medical Images (*.nii *.nii.gz *.dcm *.mhd *.mha);;All Files (*)");

    if (!fileName.isEmpty()) {
        updateStatusMessage("Loading T1 image...");
        m_statusProgress->setVisible(true);

        if (m_brainViewer->loadT1Image(fileName)) {
            updateStatusMessage("T1 image loaded successfully");
            m_startRegistrationButton->setEnabled(m_registrationManager->hasAtlasImage());
            m_registrationManager->loadT1Image(fileName);
        } else {
            updateStatusMessage("Failed to load T1 image");
            QMessageBox::warning(this, "Error", "Failed to load T1 image");
        }

        m_statusProgress->setVisible(false);
    }
}

void MainWindow::openAtlasImage()
{
    QString fileName = QFileDialog::getOpenFileName(this,
        "Open Atlas Template", "",
        "Medical Images (*.nii *.nii.gz *.dcm *.mhd *.mha);;All Files (*)");

    if (!fileName.isEmpty()) {
        updateStatusMessage("Loading atlas template...");
        m_statusProgress->setVisible(true);

        if (m_registrationManager->loadAtlasImage(fileName)) {
            updateStatusMessage("Atlas template loaded successfully");
            m_atlasManager->loadAtlasRegions(fileName);
            m_startRegistrationButton->setEnabled(m_brainViewer->hasT1Image());

            // Populate brain region tree
            populateBrainRegionTree();
        } else {
            updateStatusMessage("Failed to load atlas template");
            QMessageBox::warning(this, "Error", "Failed to load atlas template");
        }

        m_statusProgress->setVisible(false);
    }
}

void MainWindow::saveScreenshot()
{
    QString fileName = QFileDialog::getSaveFileName(this,
        "Save Screenshot", "",
        "PNG Images (*.png);;JPEG Images (*.jpg);;All Files (*)");

    if (!fileName.isEmpty()) {
        // Implementation for saving screenshot
        updateStatusMessage("Screenshot saved: " + QFileInfo(fileName).baseName());
    }
}

void MainWindow::exportMesh()
{
    QString fileName = QFileDialog::getSaveFileName(this,
        "Export Mesh", "",
        "STL Files (*.stl);;OBJ Files (*.obj);;PLY Files (*.ply);;All Files (*)");

    if (!fileName.isEmpty()) {
        // Implementation for exporting mesh
        updateStatusMessage("Mesh exported: " + QFileInfo(fileName).baseName());
    }
}

void MainWindow::resetView()
{
    m_brainViewer->resetCamera();
}

void MainWindow::setAxialView()
{
    m_brainViewer->setViewAngle(0, 0);
}

void MainWindow::setSagittalView()
{
    m_brainViewer->setViewAngle(90, 0);
}

void MainWindow::setCoronalView()
{
    m_brainViewer->setViewAngle(0, 90);
}

void MainWindow::toggleWireframe(bool enabled)
{
    m_brainViewer->setWireframeMode(enabled);
}

void MainWindow::toggleVolumeRendering(bool enabled)
{
    m_brainViewer->setVolumeRenderingEnabled(enabled);
}

void MainWindow::toggleAtlasVisibility(bool visible)
{
    m_brainViewer->setAtlasVisible(visible);
}

void MainWindow::onRegistrationProgress(int progress)
{
    m_registrationProgress->setValue(progress);
    m_statusProgress->setValue(progress);
    updateStatusMessage(QString("Registration progress: %1%").arg(progress));
}

void MainWindow::onRegistrationCompleted()
{
    m_registrationProgress->setValue(100);
    m_statusProgress->setVisible(false);
    updateStatusMessage("Registration completed successfully");

    // Enable atlas visualization
    m_showAtlasCheckBox->setEnabled(true);
    m_brainRegionTree->setEnabled(true);

    // Load registered atlas into brain viewer
    if (m_registrationManager->getRegisteredAtlas()) {
        // Implementation to load registered atlas
    }
}

void MainWindow::onBrainRegionSelected(const QString &regionName)
{
    m_brainViewer->highlightBrainRegion(regionName);

    // Add to selected regions list if not already there
    bool found = false;
    for (int i = 0; i < m_selectedRegionsList->count(); ++i) {
        if (m_selectedRegionsList->item(i)->text() == regionName) {
            found = true;
            break;
        }
    }

    if (!found) {
        m_selectedRegionsList->addItem(regionName);
    }

    updateStatusMessage(QString("Selected brain region: %1").arg(regionName));
}

void MainWindow::onBrainRegionClicked(const QString &regionName)
{
    onBrainRegionSelected(regionName);
}

void MainWindow::clearRegionSelection()
{
    m_selectedRegionsList->clear();
    m_brainViewer->clearHighlights();
    updateStatusMessage("Region selection cleared");
}

void MainWindow::updateOpacity(int value)
{
    double opacity = value / 100.0;
    m_brainViewer->setOpacity(opacity);
}

void MainWindow::updateThreshold(double value)
{
    m_brainViewer->setThreshold(value);
}

void MainWindow::updateAtlasOpacity(int value)
{
    double opacity = value / 100.0;
    m_brainViewer->setAtlasOpacity(opacity);
}

void MainWindow::updateStatusMessage(const QString &message)
{
    m_statusLabel->setText(message);
}

void MainWindow::populateBrainRegionTree()
{
    m_brainRegionTree->clear();

    QStringList categories = m_atlasManager->getRegionCategories();
    for (const QString &category : categories) {
        QTreeWidgetItem *categoryItem = new QTreeWidgetItem(m_brainRegionTree);
        categoryItem->setText(0, category);
        categoryItem->setExpanded(true);

        QStringList regions = m_atlasManager->getRegionsInCategory(category);
        for (const QString &region : regions) {
            QTreeWidgetItem *regionItem = new QTreeWidgetItem(categoryItem);
            regionItem->setText(0, region);

            // Set color indicator
            BrainRegion regionData = m_atlasManager->getRegion(region);
            QPixmap colorPixmap(16, 16);
            colorPixmap.fill(regionData.color);
            regionItem->setIcon(0, QIcon(colorPixmap));
        }
    }
}
