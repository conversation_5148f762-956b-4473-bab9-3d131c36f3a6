#ifndef BRAINVIEWER_H
#define BRAINVIEWER_H

#include <QWidget>
#include <QColor>
#include <QMap>
#include <QString>

// VTK includes
#include <QVTKOpenGLNativeWidget.h>
#include <vtkSmartPointer.h>
#include <vtkRenderer.h>
#include <vtkRenderWindow.h>
#include <vtkRenderWindowInteractor.h>
#include <vtkInteractorStyleTrackballCamera.h>
#include <vtkNIFTIImageReader.h>
#include <vtkImageData.h>
#include <vtkMarchingCubes.h>
#include <vtkPolyDataMapper.h>
#include <vtkActor.h>
#include <vtkVolume.h>
#include <vtkVolumeMapper.h>
#include <vtkVolumeProperty.h>
#include <vtkColorTransferFunction.h>
#include <vtkPiecewiseFunction.h>
#include <vtkLookupTable.h>
#include <vtkPolyData.h>

class BrainViewer : public QVTKOpenGLNativeWidget
{
    Q_OBJECT

public:
    explicit BrainViewer(QWidget *parent = nullptr);
    ~BrainViewer();

    // Image loading
    bool loadT1Image(const QString &fileName);
    bool loadAtlasImage(const QString &fileName);
    
    // Getters
    bool hasT1Image() const { return m_t1ImageData != nullptr; }
    bool hasAtlasImage() const { return m_atlasImageData != nullptr; }
    
    // Visualization controls
    void setOpacity(double opacity);
    void setThreshold(double threshold);
    void setWireframeMode(bool enabled);
    void setAtlasVisible(bool visible);
    void setVolumeRenderingEnabled(bool enabled);
    
    // Camera controls
    void resetCamera();
    void setViewAngle(double azimuth, double elevation);
    
    // Brain region controls
    void highlightBrainRegion(const QString &regionName);
    void clearHighlights();
    void setBrainRegionColor(const QString &regionName, const QColor &color);
    void setAtlasOpacity(double opacity);
    void showBrainRegion(const QString &regionName, bool visible);

public slots:
    void updateVisualization();

signals:
    void imageLoaded(const QString &fileName);
    void brainRegionClicked(const QString &regionName);
    void visualizationUpdated();

private slots:
    void onCellPicked(vtkObject *caller, unsigned long eventId, void *callData);

private:
    // Setup methods
    void setupRenderer();
    void setupInteractor();
    void setupBrainRegionColors();
    
    // Visualization creation
    void createT1Visualization();
    void createAtlasVisualization();
    
    // Image processing
    vtkSmartPointer<vtkImageData> preprocessImage(vtkSmartPointer<vtkImageData> image);
    vtkSmartPointer<vtkPolyData> createSurfaceMesh(vtkSmartPointer<vtkImageData> image, double threshold);

    // VTK components
    vtkSmartPointer<vtkRenderer> m_renderer;
    vtkSmartPointer<vtkRenderWindow> m_renderWindow;
    vtkSmartPointer<vtkRenderWindowInteractor> m_interactor;
    vtkSmartPointer<vtkInteractorStyleTrackballCamera> m_interactorStyle;
    
    // T1 image components
    vtkSmartPointer<vtkNIFTIImageReader> m_t1Reader;
    vtkSmartPointer<vtkImageData> m_t1ImageData;
    vtkSmartPointer<vtkMarchingCubes> m_marchingCubes;
    vtkSmartPointer<vtkPolyDataMapper> m_t1Mapper;
    vtkSmartPointer<vtkActor> m_t1Actor;
    
    // Volume rendering components
    vtkSmartPointer<vtkVolumeMapper> m_volumeMapper;
    vtkSmartPointer<vtkVolume> m_volumeActor;
    vtkSmartPointer<vtkVolumeProperty> m_volumeProperty;
    vtkSmartPointer<vtkColorTransferFunction> m_colorTransferFunction;
    vtkSmartPointer<vtkPiecewiseFunction> m_opacityTransferFunction;
    
    // Atlas components
    vtkSmartPointer<vtkNIFTIImageReader> m_atlasReader;
    vtkSmartPointer<vtkImageData> m_atlasImageData;
    vtkSmartPointer<vtkPolyDataMapper> m_atlasMapper;
    vtkSmartPointer<vtkActor> m_atlasActor;
    vtkSmartPointer<vtkLookupTable> m_atlasLookupTable;
    
    // Brain region management
    QMap<QString, QColor> m_brainRegionColors;
    QMap<QString, vtkSmartPointer<vtkActor>> m_regionActors;
    
    // Visualization parameters
    double m_currentOpacity;
    double m_currentThreshold;
    bool m_wireframeMode;
    bool m_atlasVisible;
    bool m_volumeRenderingEnabled;
};

#endif // BRAINVIEWER_H
