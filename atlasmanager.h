#ifndef ATLASMANAGER_H
#define ATLASMANAGER_H

#include <QObject>
#include <QString>
#include <QStringList>
#include <QMap>
#include <QColor>
#include <QJsonObject>
#include <QJsonDocument>

// VTK includes
#include <vtkSmartPointer.h>
#include <vtkImageData.h>
#include <vtkPolyData.h>

struct BrainRegion {
    int id;
    QString name;
    QString fullName;
    QString category;
    QColor color;
    bool visible;
    double volume;
    QString description;
    QStringList synonyms;
    
    BrainRegion() : id(0), visible(true), volume(0.0) {}
};

class AtlasManager : public QObject
{
    Q_OBJECT

public:
    explicit AtlasManager(QObject *parent = nullptr);
    ~AtlasManager();

    // Atlas loading and management
    bool loadAtlasRegions(const QString &atlasFileName);
    bool loadRegionDefinitions(const QString &definitionFile);
    bool saveRegionDefinitions(const QString &definitionFile);
    
    // Region access
    QStringList getRegionNames() const;
    QStringList getRegionCategories() const;
    QStringList getRegionsInCategory(const QString &category) const;
    BrainRegion getRegion(const QString &name) const;
    BrainRegion getRegion(int id) const;
    
    // Region properties
    void setRegionColor(const QString &regionName, const QColor &color);
    void setRegionVisibility(const QString &regionName, bool visible);
    void setRegionDescription(const QString &regionName, const QString &description);
    
    // Region analysis
    double calculateRegionVolume(const QString &regionName, vtkSmartPointer<vtkImageData> image);
    QMap<QString, double> calculateAllRegionVolumes(vtkSmartPointer<vtkImageData> image);
    
    // Region mesh generation
    vtkSmartPointer<vtkPolyData> generateRegionMesh(const QString &regionName, 
                                                   vtkSmartPointer<vtkImageData> image);
    
    // Atlas information
    QString getAtlasName() const { return m_atlasName; }
    QString getAtlasDescription() const { return m_atlasDescription; }
    QString getAtlasVersion() const { return m_atlasVersion; }
    int getRegionCount() const { return m_regions.size(); }
    
    // Region lookup
    QString getRegionNameFromId(int id) const;
    int getRegionIdFromName(const QString &name) const;
    QString getRegionNameFromPosition(double x, double y, double z, 
                                     vtkSmartPointer<vtkImageData> atlasImage) const;

public slots:
    void updateRegionColors();
    void resetRegionVisibility();
    void hideAllRegions();
    void showAllRegions();

signals:
    void regionAdded(const QString &regionName);
    void regionRemoved(const QString &regionName);
    void regionUpdated(const QString &regionName);
    void atlasLoaded(const QString &atlasName);

private:
    void initializeDefaultRegions();
    void loadStandardBrainAtlas();
    void generateRegionColors();
    QColor generateDistinctColor(int index);
    
    // Parse different atlas formats
    bool parseFreeSurferAtlas(const QString &fileName);
    bool parseAALAtlas(const QString &fileName);
    bool parseBrodmannAtlas(const QString &fileName);
    bool parseCustomAtlas(const QString &fileName);
    
    // Region data
    QMap<QString, BrainRegion> m_regions;
    QMap<int, QString> m_idToName;
    QMap<QString, QStringList> m_categories;
    
    // Atlas metadata
    QString m_atlasName;
    QString m_atlasDescription;
    QString m_atlasVersion;
    QString m_atlasFileName;
    
    // Default brain regions with standard colors
    void setupStandardRegions();
};

#endif // ATLASMANAGER_H
