#ifndef REGISTRATIONMANAGER_H
#define REGISTRATIONMANAGER_H

#include <QObject>
#include <QString>
#include <QTimer>
#include <QThread>
#include <QMutex>

// VTK includes
#include <vtkSmartPointer.h>
#include <vtkImageData.h>
#include <vtkNIFTIImageReader.h>
#include <vtkImageReslice.h>
#include <vtkTransform.h>
#include <vtkMatrix4x4.h>

class RegistrationWorker;

class RegistrationManager : public QObject
{
    Q_OBJECT

public:
    explicit RegistrationManager(QObject *parent = nullptr);
    ~RegistrationManager();

    // Registration parameters structure
    struct RegistrationParams {
        int maxIterations = 1000;
        double learningRate = 0.01;
        double convergenceThreshold = 1e-6;
        bool useMultiResolution = true;
        int numberOfLevels = 3;
        bool useGradientDescent = true;
    };

    // Image loading
    bool loadAtlasImage(const QString &fileName);
    bool loadT1Image(const QString &fileName);
    
    // Registration status
    bool hasAtlasImage() const { return m_atlasImageData != nullptr; }
    bool hasT1Image() const { return m_t1ImageData != nullptr; }
    bool isRegistrationRunning() const { return m_registrationRunning; }
    
    // Get registered images
    vtkSmartPointer<vtkImageData> getRegisteredAtlas() const { return m_registeredAtlasData; }
    vtkSmartPointer<vtkTransform> getRegistrationTransform() const { return m_registrationTransform; }

public slots:
    void startRegistration();
    void stopRegistration();

signals:
    void progressUpdated(int progress);
    void registrationCompleted();
    void registrationFailed(const QString &error);
    void statusChanged(const QString &status);

private slots:
    void onRegistrationProgress(int progress);
    void onRegistrationFinished();
    void onRegistrationError(const QString &error);

private:
    void initializeRegistration();
    void performRigidRegistration();
    void performAffineRegistration();
    void performNonLinearRegistration();
    void applyTransformation();
    
    // Image preprocessing
    vtkSmartPointer<vtkImageData> preprocessImage(vtkSmartPointer<vtkImageData> image);
    void normalizeImage(vtkSmartPointer<vtkImageData> image);
    void resampleImage(vtkSmartPointer<vtkImageData> image, double spacing[3]);
    
    // Registration algorithms
    bool performITKRegistration();
    bool performVTKRegistration();
    
    // Image data
    vtkSmartPointer<vtkNIFTIImageReader> m_atlasReader;
    vtkSmartPointer<vtkNIFTIImageReader> m_t1Reader;
    vtkSmartPointer<vtkImageData> m_atlasImageData;
    vtkSmartPointer<vtkImageData> m_t1ImageData;
    vtkSmartPointer<vtkImageData> m_registeredAtlasData;
    
    // Registration transform
    vtkSmartPointer<vtkTransform> m_registrationTransform;
    vtkSmartPointer<vtkMatrix4x4> m_transformMatrix;
    
    // Registration parameters
    RegistrationParams m_params;
    
    // Threading
    QThread *m_workerThread;
    RegistrationWorker *m_worker;
    QMutex m_dataMutex;
    
    // Status
    bool m_registrationRunning;
    int m_currentProgress;
    QString m_currentStatus;
    
    // File paths
    QString m_atlasFileName;
    QString m_t1FileName;
};

// Worker class for threaded registration
class RegistrationWorker : public QObject
{
    Q_OBJECT

public:
    explicit RegistrationWorker(QObject *parent = nullptr);
    
    void setImageData(vtkSmartPointer<vtkImageData> atlas, vtkSmartPointer<vtkImageData> t1);
    void setParameters(const RegistrationManager::RegistrationParams &params);

public slots:
    void performRegistration();
    void stopRegistration();

signals:
    void progressUpdated(int progress);
    void registrationFinished();
    void registrationError(const QString &error);
    void transformUpdated(vtkSmartPointer<vtkTransform> transform);

private:
    vtkSmartPointer<vtkImageData> m_atlasData;
    vtkSmartPointer<vtkImageData> m_t1Data;
    RegistrationManager::RegistrationParams m_params;
    bool m_stopRequested;
    
    // Registration implementation
    bool performMutualInformationRegistration();
    bool performCorrelationRegistration();
    double calculateMutualInformation(vtkSmartPointer<vtkImageData> img1, 
                                     vtkSmartPointer<vtkImageData> img2);
};

#endif // REGISTRATIONMANAGER_H
