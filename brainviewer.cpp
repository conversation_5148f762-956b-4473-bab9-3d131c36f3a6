#include "brainviewer.h"

// VTK includes
#include <vtkNIFTIImageReader.h>
#include <vtkMarchingCubes.h>
#include <vtkPolyDataMapper.h>
#include <vtkActor.h>
#include <vtkProperty.h>
#include <vtkRenderer.h>
#include <vtkRenderWindow.h>
#include <vtkRenderWindowInteractor.h>
#include <vtkInteractorStyleTrackballCamera.h>
#include <vtkCamera.h>
#include <vtkLookupTable.h>
#include <vtkColorTransferFunction.h>
#include <vtkPiecewiseFunction.h>
#include <vtkVolumeProperty.h>
#include <vtkFixedPointVolumeRayCastMapper.h>
#include <vtkVolume.h>
#include <vtkImageGaussianSmooth.h>
#include <vtkImageThreshold.h>
#include <vtkImageCast.h>
#include <vtkCellPicker.h>
#include <vtkCallbackCommand.h>
#include <vtkPointData.h>
#include <vtkDataArray.h>

#include <QDebug>
#include <QFileInfo>

BrainViewer::BrainViewer(QWidget *parent)
    : QVTKOpenGLNativeWidget(parent)
    , m_currentOpacity(1.0)
    , m_currentThreshold(100.0)
    , m_wireframeMode(false)
    , m_atlasVisible(false)
    , m_volumeRenderingEnabled(true)
{
    setupRenderer();
    setupInteractor();
    setupBrainRegionColors();
}

BrainViewer::~BrainViewer()
{
}

void BrainViewer::setupRenderer()
{
    m_renderer = vtkSmartPointer<vtkRenderer>::New();
    m_renderer->SetBackground(0.1, 0.1, 0.1); // Dark background
    m_renderer->SetBackground2(0.2, 0.2, 0.2);
    m_renderer->SetGradientBackground(true);

    m_renderWindow = vtkSmartPointer<vtkRenderWindow>::New();
    m_renderWindow->AddRenderer(m_renderer);

    SetRenderWindow(m_renderWindow);
}

void BrainViewer::setupInteractor()
{
    m_interactor = m_renderWindow->GetInteractor();
    m_interactorStyle = vtkSmartPointer<vtkInteractorStyleTrackballCamera>::New();
    m_interactor->SetInteractorStyle(m_interactorStyle);

    // Setup cell picker for brain region selection
    vtkSmartPointer<vtkCellPicker> picker = vtkSmartPointer<vtkCellPicker>::New();
    m_interactor->SetPicker(picker);

    // Add callback for picking events
    vtkSmartPointer<vtkCallbackCommand> pickCallback = vtkSmartPointer<vtkCallbackCommand>::New();
    pickCallback->SetCallback([](vtkObject* caller, long unsigned int eventId, void* clientData, void* callData) {
        BrainViewer* viewer = static_cast<BrainViewer*>(clientData);
        viewer->onCellPicked(caller, eventId, callData);
    });
    pickCallback->SetClientData(this);

    m_interactor->AddObserver(vtkCommand::LeftButtonPressEvent, pickCallback);
}

bool BrainViewer::loadT1Image(const QString &fileName)
{
    QFileInfo fileInfo(fileName);
    if (!fileInfo.exists()) {
        qWarning() << "T1 image file does not exist:" << fileName;
        return false;
    }

    m_t1Reader = vtkSmartPointer<vtkNIFTIImageReader>::New();
    m_t1Reader->SetFileName(fileName.toStdString().c_str());

    try {
        m_t1Reader->Update();
        m_t1ImageData = m_t1Reader->GetOutput();

        if (!m_t1ImageData) {
            qWarning() << "Failed to read T1 image data";
            return false;
        }

        createT1Visualization();
        updateVisualization();
        resetCamera();

        emit imageLoaded(fileName);
        return true;
    }
    catch (const std::exception& e) {
        qWarning() << "Exception loading T1 image:" << e.what();
        return false;
    }
}

bool BrainViewer::loadAtlasImage(const QString &fileName)
{
    QFileInfo fileInfo(fileName);
    if (!fileInfo.exists()) {
        qWarning() << "Atlas image file does not exist:" << fileName;
        return false;
    }

    m_atlasReader = vtkSmartPointer<vtkNIFTIImageReader>::New();
    m_atlasReader->SetFileName(fileName.toStdString().c_str());

    try {
        m_atlasReader->Update();
        m_atlasImageData = m_atlasReader->GetOutput();

        if (!m_atlasImageData) {
            qWarning() << "Failed to read atlas image data";
            return false;
        }

        createAtlasVisualization();

        return true;
    }
    catch (const std::exception& e) {
        qWarning() << "Exception loading atlas image:" << e.what();
        return false;
    }
}

void BrainViewer::createT1Visualization()
{
    if (!m_t1ImageData) return;

    // Preprocess the image
    vtkSmartPointer<vtkImageData> processedImage = preprocessImage(m_t1ImageData);

    if (m_volumeRenderingEnabled) {
        // Volume rendering
        m_volumeMapper = vtkSmartPointer<vtkFixedPointVolumeRayCastMapper>::New();
        m_volumeMapper->SetInputData(processedImage);

        // Create volume property
        m_volumeProperty = vtkSmartPointer<vtkVolumeProperty>::New();

        // Color transfer function
        m_colorTransferFunction = vtkSmartPointer<vtkColorTransferFunction>::New();
        m_colorTransferFunction->AddRGBPoint(0, 0.0, 0.0, 0.0);
        m_colorTransferFunction->AddRGBPoint(50, 0.2, 0.2, 0.2);
        m_colorTransferFunction->AddRGBPoint(100, 0.8, 0.8, 0.8);
        m_colorTransferFunction->AddRGBPoint(255, 1.0, 1.0, 1.0);

        // Opacity transfer function
        m_opacityTransferFunction = vtkSmartPointer<vtkPiecewiseFunction>::New();
        m_opacityTransferFunction->AddPoint(0, 0.0);
        m_opacityTransferFunction->AddPoint(50, 0.1);
        m_opacityTransferFunction->AddPoint(100, 0.3);
        m_opacityTransferFunction->AddPoint(255, 0.8);

        m_volumeProperty->SetColor(m_colorTransferFunction);
        m_volumeProperty->SetScalarOpacity(m_opacityTransferFunction);
        m_volumeProperty->ShadeOn();
        m_volumeProperty->SetInterpolationTypeToLinear();

        // Create volume actor
        m_volumeActor = vtkSmartPointer<vtkVolume>::New();
        m_volumeActor->SetMapper(m_volumeMapper);
        m_volumeActor->SetProperty(m_volumeProperty);

        m_renderer->AddVolume(m_volumeActor);
    } else {
        // Surface rendering using marching cubes
        m_marchingCubes = vtkSmartPointer<vtkMarchingCubes>::New();
        m_marchingCubes->SetInputData(processedImage);
        m_marchingCubes->SetValue(0, m_currentThreshold);
        m_marchingCubes->Update();

        m_t1Mapper = vtkSmartPointer<vtkPolyDataMapper>::New();
        m_t1Mapper->SetInputConnection(m_marchingCubes->GetOutputPort());
        m_t1Mapper->ScalarVisibilityOff();

        m_t1Actor = vtkSmartPointer<vtkActor>::New();
        m_t1Actor->SetMapper(m_t1Mapper);
        m_t1Actor->GetProperty()->SetColor(0.9, 0.9, 0.9);
        m_t1Actor->GetProperty()->SetOpacity(m_currentOpacity);

        if (m_wireframeMode) {
            m_t1Actor->GetProperty()->SetRepresentationToWireframe();
        } else {
            m_t1Actor->GetProperty()->SetRepresentationToSurface();
        }

        m_renderer->AddActor(m_t1Actor);
    }
}

void BrainViewer::createAtlasVisualization()
{
    if (!m_atlasImageData) return;

    // Create lookup table for atlas regions
    m_atlasLookupTable = vtkSmartPointer<vtkLookupTable>::New();
    m_atlasLookupTable->SetNumberOfTableValues(256);
    m_atlasLookupTable->SetRange(0, 255);

    // Set colors for different brain regions
    for (int i = 0; i < 256; ++i) {
        if (i == 0) {
            m_atlasLookupTable->SetTableValue(i, 0.0, 0.0, 0.0, 0.0); // Transparent background
        } else {
            // Generate distinct colors for each region
            double hue = (i * 137.5) / 360.0; // Golden angle for good color distribution
            double saturation = 0.8;
            double value = 0.9;
            double r, g, b;

            // HSV to RGB conversion
            int h_i = static_cast<int>(hue * 6);
            double f = hue * 6 - h_i;
            double p = value * (1 - saturation);
            double q = value * (1 - f * saturation);
            double t = value * (1 - (1 - f) * saturation);

            switch (h_i % 6) {
                case 0: r = value; g = t; b = p; break;
                case 1: r = q; g = value; b = p; break;
                case 2: r = p; g = value; b = t; break;
                case 3: r = p; g = q; b = value; break;
                case 4: r = t; g = p; b = value; break;
                case 5: r = value; g = p; b = q; break;
                default: r = g = b = 0; break;
            }

            m_atlasLookupTable->SetTableValue(i, r, g, b, 0.6);
        }
    }

    m_atlasLookupTable->Build();

    // Create surface from atlas
    vtkSmartPointer<vtkMarchingCubes> atlasMarchingCubes = vtkSmartPointer<vtkMarchingCubes>::New();
    atlasMarchingCubes->SetInputData(m_atlasImageData);
    atlasMarchingCubes->SetValue(0, 0.5);
    atlasMarchingCubes->Update();

    m_atlasMapper = vtkSmartPointer<vtkPolyDataMapper>::New();
    m_atlasMapper->SetInputConnection(atlasMarchingCubes->GetOutputPort());
    m_atlasMapper->SetLookupTable(m_atlasLookupTable);
    m_atlasMapper->SetScalarRange(0, 255);

    m_atlasActor = vtkSmartPointer<vtkActor>::New();
    m_atlasActor->SetMapper(m_atlasMapper);
    m_atlasActor->SetVisibility(m_atlasVisible);

    m_renderer->AddActor(m_atlasActor);
}

vtkSmartPointer<vtkImageData> BrainViewer::preprocessImage(vtkSmartPointer<vtkImageData> image)
{
    // Apply Gaussian smoothing
    vtkSmartPointer<vtkImageGaussianSmooth> smoother = vtkSmartPointer<vtkImageGaussianSmooth>::New();
    smoother->SetInputData(image);
    smoother->SetStandardDeviations(1.0, 1.0, 1.0);
    smoother->SetRadiusFactors(2.0, 2.0, 2.0);
    smoother->Update();

    return smoother->GetOutput();
}

vtkSmartPointer<vtkPolyData> BrainViewer::createSurfaceMesh(vtkSmartPointer<vtkImageData> image, double threshold)
{
    vtkSmartPointer<vtkMarchingCubes> marchingCubes = vtkSmartPointer<vtkMarchingCubes>::New();
    marchingCubes->SetInputData(image);
    marchingCubes->SetValue(0, threshold);
    marchingCubes->Update();

    return marchingCubes->GetOutput();
}

void BrainViewer::updateVisualization()
{
    if (m_renderWindow) {
        m_renderWindow->Render();
    }
}

void BrainViewer::setupBrainRegionColors()
{
    // Define standard brain region colors
    m_brainRegionColors["Frontal Lobe"] = QColor(255, 100, 100);
    m_brainRegionColors["Parietal Lobe"] = QColor(100, 255, 100);
    m_brainRegionColors["Temporal Lobe"] = QColor(100, 100, 255);
    m_brainRegionColors["Occipital Lobe"] = QColor(255, 255, 100);
    m_brainRegionColors["Cerebellum"] = QColor(255, 100, 255);
    m_brainRegionColors["Brain Stem"] = QColor(100, 255, 255);
    m_brainRegionColors["Corpus Callosum"] = QColor(200, 200, 200);
    m_brainRegionColors["Hippocampus"] = QColor(255, 150, 50);
    m_brainRegionColors["Amygdala"] = QColor(150, 50, 255);
    m_brainRegionColors["Thalamus"] = QColor(50, 255, 150);
    m_brainRegionColors["Caudate"] = QColor(255, 50, 150);
    m_brainRegionColors["Putamen"] = QColor(150, 255, 50);
}

// Visualization control methods
void BrainViewer::setOpacity(double opacity)
{
    m_currentOpacity = opacity;

    if (m_t1Actor) {
        m_t1Actor->GetProperty()->SetOpacity(opacity);
    }

    if (m_volumeProperty && m_opacityTransferFunction) {
        // Scale the opacity transfer function
        vtkSmartPointer<vtkPiecewiseFunction> newOpacity = vtkSmartPointer<vtkPiecewiseFunction>::New();
        newOpacity->AddPoint(0, 0.0);
        newOpacity->AddPoint(50, 0.1 * opacity);
        newOpacity->AddPoint(100, 0.3 * opacity);
        newOpacity->AddPoint(255, 0.8 * opacity);
        m_volumeProperty->SetScalarOpacity(newOpacity);
    }

    updateVisualization();
}

void BrainViewer::setThreshold(double threshold)
{
    m_currentThreshold = threshold;

    if (m_marchingCubes) {
        m_marchingCubes->SetValue(0, threshold);
        m_marchingCubes->Update();
        updateVisualization();
    }
}

void BrainViewer::setWireframeMode(bool enabled)
{
    m_wireframeMode = enabled;

    if (m_t1Actor) {
        if (enabled) {
            m_t1Actor->GetProperty()->SetRepresentationToWireframe();
        } else {
            m_t1Actor->GetProperty()->SetRepresentationToSurface();
        }
        updateVisualization();
    }
}

void BrainViewer::setAtlasVisible(bool visible)
{
    m_atlasVisible = visible;

    if (m_atlasActor) {
        m_atlasActor->SetVisibility(visible);
        updateVisualization();
    }
}

void BrainViewer::setVolumeRenderingEnabled(bool enabled)
{
    if (m_volumeRenderingEnabled == enabled) return;

    m_volumeRenderingEnabled = enabled;

    // Remove existing actors/volumes
    if (m_t1Actor) {
        m_renderer->RemoveActor(m_t1Actor);
        m_t1Actor = nullptr;
    }
    if (m_volumeActor) {
        m_renderer->RemoveVolume(m_volumeActor);
        m_volumeActor = nullptr;
    }

    // Recreate visualization with new mode
    if (m_t1ImageData) {
        createT1Visualization();
        updateVisualization();
    }
}

void BrainViewer::resetCamera()
{
    if (m_renderer) {
        m_renderer->ResetCamera();
        updateVisualization();
    }
}

void BrainViewer::setViewAngle(double azimuth, double elevation)
{
    if (m_renderer) {
        vtkCamera* camera = m_renderer->GetActiveCamera();
        camera->SetPosition(0, 0, 1);
        camera->SetFocalPoint(0, 0, 0);
        camera->SetViewUp(0, 1, 0);
        camera->Azimuth(azimuth);
        camera->Elevation(elevation);
        m_renderer->ResetCameraClippingRange();
        updateVisualization();
    }
}

void BrainViewer::highlightBrainRegion(const QString &regionName)
{
    // Implementation for highlighting specific brain regions
    if (m_brainRegionColors.contains(regionName)) {
        QColor color = m_brainRegionColors[regionName];
        // Update the lookup table or create a separate actor for highlighting
        qDebug() << "Highlighting brain region:" << regionName << "with color:" << color.name();
    }
}

void BrainViewer::clearHighlights()
{
    // Clear all region highlights
    for (auto it = m_regionActors.begin(); it != m_regionActors.end(); ++it) {
        m_renderer->RemoveActor(it.value());
    }
    m_regionActors.clear();
    updateVisualization();
}

void BrainViewer::setBrainRegionColor(const QString &regionName, const QColor &color)
{
    m_brainRegionColors[regionName] = color;
}

void BrainViewer::setAtlasOpacity(double opacity)
{
    if (m_atlasActor) {
        m_atlasActor->GetProperty()->SetOpacity(opacity);
        updateVisualization();
    }
}

void BrainViewer::showBrainRegion(const QString &regionName, bool visible)
{
    if (m_regionActors.contains(regionName)) {
        m_regionActors[regionName]->SetVisibility(visible);
        updateVisualization();
    }
}

void BrainViewer::onCellPicked(vtkObject *caller, unsigned long eventId, void *callData)
{
    Q_UNUSED(caller)
    Q_UNUSED(eventId)
    Q_UNUSED(callData)

    vtkCellPicker* picker = static_cast<vtkCellPicker*>(m_interactor->GetPicker());
    if (picker->GetCellId() != -1) {
        // Get the picked point and determine which brain region it belongs to
        double* pos = picker->GetPickPosition();

        // For now, emit a generic region name
        // In a real implementation, you would map the picked position to atlas regions
        emit brainRegionClicked("Unknown Region");

        qDebug() << "Picked position:" << pos[0] << pos[1] << pos[2];
    }
}
