#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QSplitter>
#include <QTabWidget>
#include <QGroupBox>
#include <QPushButton>
#include <QSlider>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QCheckBox>
#include <QComboBox>
#include <QLabel>
#include <QProgressBar>
#include <QStatusBar>
#include <QMenuBar>
#include <QToolBar>
#include <QAction>
#include <QTreeWidget>
#include <QListWidget>
#include <QTextEdit>
#include <QFileDialog>
#include <QMessageBox>

class BrainViewer;
class RegistrationManager;
class AtlasManager;

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    // File operations
    void openT1Image();
    void openAtlasImage();
    void saveScreenshot();
    void exportMesh();
    
    // View controls
    void resetView();
    void setAxialView();
    void setSagittalView();
    void setCoronalView();
    void toggleWireframe(bool enabled);
    void toggleVolumeRendering(bool enabled);
    void toggleAtlasVisibility(bool visible);
    
    // Registration
    void onRegistrationProgress(int progress);
    void onRegistrationCompleted();
    
    // Brain region interaction
    void onBrainRegionSelected(const QString &regionName);
    void onBrainRegionClicked(const QString &regionName);
    void clearRegionSelection();
    
    // Visualization controls
    void updateOpacity(int value);
    void updateThreshold(double value);
    void updateAtlasOpacity(int value);
    
    // Status updates
    void updateStatusMessage(const QString &message);

private:
    void setupUI();
    void setupMenuBar();
    void setupToolBar();
    void setupStatusBar();
    void connectSignals();
    
    void createVisualizationTab();
    void createRegistrationTab();
    void createAtlasTab();
    void createAnalysisTab();

    // Central widget and layout
    QWidget *m_centralWidget;
    QHBoxLayout *m_mainLayout;
    QSplitter *m_mainSplitter;
    
    // Brain viewer
    BrainViewer *m_brainViewer;
    
    // Managers
    RegistrationManager *m_registrationManager;
    AtlasManager *m_atlasManager;
    
    // Control panels
    QTabWidget *m_controlTabs;
    QWidget *m_visualizationTab;
    QWidget *m_registrationTab;
    QWidget *m_atlasTab;
    QWidget *m_analysisTab;
    
    // Visualization controls
    QGroupBox *m_renderingGroup;
    QSlider *m_opacitySlider;
    QDoubleSpinBox *m_thresholdSpinBox;
    QCheckBox *m_wireframeCheckBox;
    QCheckBox *m_volumeRenderingCheckBox;
    QComboBox *m_renderingModeCombo;
    
    QGroupBox *m_viewGroup;
    QPushButton *m_resetViewButton;
    QPushButton *m_axialViewButton;
    QPushButton *m_sagittalViewButton;
    QPushButton *m_coronalViewButton;
    
    // Registration controls
    QGroupBox *m_registrationGroup;
    QPushButton *m_loadT1Button;
    QPushButton *m_loadAtlasButton;
    QPushButton *m_startRegistrationButton;
    QProgressBar *m_registrationProgress;
    QLabel *m_registrationStatus;
    
    // Atlas controls
    QGroupBox *m_atlasGroup;
    QCheckBox *m_showAtlasCheckBox;
    QSlider *m_atlasOpacitySlider;
    QTreeWidget *m_brainRegionTree;
    QListWidget *m_selectedRegionsList;
    QPushButton *m_clearSelectionButton;
    
    // Analysis controls
    QGroupBox *m_analysisGroup;
    QTextEdit *m_analysisResults;
    QPushButton *m_calculateVolumeButton;
    QPushButton *m_exportDataButton;
    
    // Menu and toolbar
    QMenuBar *m_menuBar;
    QMenu *m_fileMenu;
    QMenu *m_viewMenu;
    QMenu *m_toolsMenu;
    QMenu *m_helpMenu;
    
    QToolBar *m_mainToolBar;
    QAction *m_openT1Action;
    QAction *m_openAtlasAction;
    QAction *m_saveScreenshotAction;
    QAction *m_exitAction;
    QAction *m_aboutAction;
    
    // Status bar
    QStatusBar *m_statusBar;
    QLabel *m_statusLabel;
    QProgressBar *m_statusProgress;
};

#endif // MAINWINDOW_H
