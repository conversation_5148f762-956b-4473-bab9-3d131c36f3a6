@echo off
echo Building Brain Viewer 3D...

REM Create build directory if it doesn't exist
if not exist build mkdir build

REM Change to build directory
cd build

REM Configure with CMake
echo Configuring with CMake...
cmake .. -DCMAKE_BUILD_TYPE=Release

REM Check if configuration was successful
if %ERRORLEVEL% neq 0 (
    echo CMake configuration failed!
    echo Please make sure Qt6 and VTK are properly installed and in your PATH.
    pause
    exit /b 1
)

REM Build the project
echo Building project...
cmake --build . --config Release

REM Check if build was successful
if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build completed successfully!
echo Executable location: build\bin\Release\BrainViewer3D.exe
pause
