#!/bin/bash

echo "Building Brain Viewer 3D..."

# Create build directory if it doesn't exist
if [ ! -d "build" ]; then
    mkdir build
fi

# Change to build directory
cd build

# Configure with CMake
echo "Configuring with CMake..."
cmake .. -DCMAKE_BUILD_TYPE=Release

# Check if configuration was successful
if [ $? -ne 0 ]; then
    echo "CMake configuration failed!"
    echo "Please make sure Qt6 and VTK are properly installed."
    echo "You may need to set CMAKE_PREFIX_PATH to include Qt6 and VTK installation paths."
    exit 1
fi

# Build the project
echo "Building project..."
cmake --build . --config Release

# Check if build was successful
if [ $? -ne 0 ]; then
    echo "Build failed!"
    exit 1
fi

echo "Build completed successfully!"
echo "Executable location: build/bin/BrainViewer3D"
