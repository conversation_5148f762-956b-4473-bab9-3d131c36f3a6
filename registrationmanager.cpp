#include "registrationmanager.h"

// VTK includes
#include <vtkImageGaussianSmooth.h>
#include <vtkImageCast.h>
#include <vtkImageResample.h>
#include <vtkImageChangeInformation.h>
#include <vtkImageMathematics.h>
#include <vtkImageStatistics.h>

// Qt includes
#include <QDebug>
#include <QFileInfo>
#include <QApplication>

RegistrationManager::RegistrationManager(QObject *parent)
    : QObject(parent)
    , m_registrationRunning(false)
    , m_currentProgress(0)
    , m_workerThread(nullptr)
    , m_worker(nullptr)
{
    m_registrationTransform = vtkSmartPointer<vtkTransform>::New();
    m_transformMatrix = vtkSmartPointer<vtkMatrix4x4>::New();
    m_transformMatrix->Identity();
    m_registrationTransform->SetMatrix(m_transformMatrix);
}

RegistrationManager::~RegistrationManager()
{
    if (m_workerThread && m_workerThread->isRunning()) {
        m_workerThread->quit();
        m_workerThread->wait();
    }
}

bool RegistrationManager::loadAtlasImage(const QString &fileName)
{
    QFileInfo fileInfo(fileName);
    if (!fileInfo.exists()) {
        emit registrationFailed("Atlas file does not exist: " + fileName);
        return false;
    }

    m_atlasReader = vtkSmartPointer<vtkNIFTIImageReader>::New();
    m_atlasReader->SetFileName(fileName.toStdString().c_str());

    try {
        m_atlasReader->Update();
        m_atlasImageData = m_atlasReader->GetOutput();
        
        if (!m_atlasImageData) {
            emit registrationFailed("Failed to read atlas image data");
            return false;
        }

        m_atlasFileName = fileName;
        emit statusChanged("Atlas image loaded successfully");
        return true;
    }
    catch (const std::exception& e) {
        emit registrationFailed(QString("Exception loading atlas: %1").arg(e.what()));
        return false;
    }
}

bool RegistrationManager::loadT1Image(const QString &fileName)
{
    QFileInfo fileInfo(fileName);
    if (!fileInfo.exists()) {
        emit registrationFailed("T1 file does not exist: " + fileName);
        return false;
    }

    m_t1Reader = vtkSmartPointer<vtkNIFTIImageReader>::New();
    m_t1Reader->SetFileName(fileName.toStdString().c_str());

    try {
        m_t1Reader->Update();
        m_t1ImageData = m_t1Reader->GetOutput();
        
        if (!m_t1ImageData) {
            emit registrationFailed("Failed to read T1 image data");
            return false;
        }

        m_t1FileName = fileName;
        emit statusChanged("T1 image loaded successfully");
        return true;
    }
    catch (const std::exception& e) {
        emit registrationFailed(QString("Exception loading T1: %1").arg(e.what()));
        return false;
    }
}

void RegistrationManager::startRegistration()
{
    if (m_registrationRunning) {
        qWarning() << "Registration already running";
        return;
    }

    if (!m_atlasImageData || !m_t1ImageData) {
        emit registrationFailed("Both atlas and T1 images must be loaded before registration");
        return;
    }

    m_registrationRunning = true;
    m_currentProgress = 0;
    
    emit statusChanged("Starting registration...");
    emit progressUpdated(0);

    // Create worker thread
    m_workerThread = new QThread(this);
    m_worker = new RegistrationWorker();
    m_worker->moveToThread(m_workerThread);
    
    // Set up connections
    connect(m_workerThread, &QThread::started, m_worker, &RegistrationWorker::performRegistration);
    connect(m_worker, &RegistrationWorker::progressUpdated, this, &RegistrationManager::onRegistrationProgress);
    connect(m_worker, &RegistrationWorker::registrationFinished, this, &RegistrationManager::onRegistrationFinished);
    connect(m_worker, &RegistrationWorker::registrationError, this, &RegistrationManager::onRegistrationError);
    connect(m_worker, &RegistrationWorker::transformUpdated, 
            [this](vtkSmartPointer<vtkTransform> transform) {
                m_registrationTransform = transform;
            });
    
    // Set worker data
    m_worker->setImageData(m_atlasImageData, m_t1ImageData);
    m_worker->setParameters(m_params);
    
    // Start thread
    m_workerThread->start();
}

void RegistrationManager::stopRegistration()
{
    if (m_worker) {
        m_worker->stopRegistration();
    }
    
    if (m_workerThread && m_workerThread->isRunning()) {
        m_workerThread->quit();
        m_workerThread->wait();
    }
    
    m_registrationRunning = false;
    emit statusChanged("Registration stopped");
}

void RegistrationManager::onRegistrationProgress(int progress)
{
    m_currentProgress = progress;
    emit progressUpdated(progress);
    emit statusChanged(QString("Registration progress: %1%").arg(progress));
}

void RegistrationManager::onRegistrationFinished()
{
    m_registrationRunning = false;
    
    // Apply transformation to create registered atlas
    applyTransformation();
    
    // Clean up thread
    if (m_workerThread) {
        m_workerThread->quit();
        m_workerThread->wait();
        m_workerThread->deleteLater();
        m_workerThread = nullptr;
    }
    
    if (m_worker) {
        m_worker->deleteLater();
        m_worker = nullptr;
    }
    
    emit statusChanged("Registration completed successfully");
    emit registrationCompleted();
}

void RegistrationManager::onRegistrationError(const QString &error)
{
    m_registrationRunning = false;
    
    // Clean up thread
    if (m_workerThread) {
        m_workerThread->quit();
        m_workerThread->wait();
        m_workerThread->deleteLater();
        m_workerThread = nullptr;
    }
    
    if (m_worker) {
        m_worker->deleteLater();
        m_worker = nullptr;
    }
    
    emit registrationFailed(error);
}

void RegistrationManager::applyTransformation()
{
    if (!m_atlasImageData || !m_registrationTransform) {
        return;
    }

    // Create image reslice filter
    vtkSmartPointer<vtkImageReslice> reslice = vtkSmartPointer<vtkImageReslice>::New();
    reslice->SetInputData(m_atlasImageData);
    reslice->SetResliceTransform(m_registrationTransform);
    reslice->SetInterpolationModeToLinear();
    reslice->SetOutputSpacing(m_t1ImageData->GetSpacing());
    reslice->SetOutputOrigin(m_t1ImageData->GetOrigin());
    reslice->SetOutputExtent(m_t1ImageData->GetExtent());
    reslice->Update();

    m_registeredAtlasData = reslice->GetOutput();
}

vtkSmartPointer<vtkImageData> RegistrationManager::preprocessImage(vtkSmartPointer<vtkImageData> image)
{
    // Apply Gaussian smoothing
    vtkSmartPointer<vtkImageGaussianSmooth> smoother = vtkSmartPointer<vtkImageGaussianSmooth>::New();
    smoother->SetInputData(image);
    smoother->SetStandardDeviations(1.0, 1.0, 1.0);
    smoother->Update();

    return smoother->GetOutput();
}

// RegistrationWorker implementation
RegistrationWorker::RegistrationWorker(QObject *parent)
    : QObject(parent)
    , m_stopRequested(false)
{
}

void RegistrationWorker::setImageData(vtkSmartPointer<vtkImageData> atlas, vtkSmartPointer<vtkImageData> t1)
{
    m_atlasData = atlas;
    m_t1Data = t1;
}

void RegistrationWorker::setParameters(const RegistrationManager::RegistrationParams &params)
{
    m_params = params;
}

void RegistrationWorker::performRegistration()
{
    m_stopRequested = false;
    
    try {
        emit progressUpdated(10);
        
        // Simplified registration using correlation
        if (!performCorrelationRegistration()) {
            emit registrationError("Correlation registration failed");
            return;
        }
        
        emit progressUpdated(100);
        emit registrationFinished();
    }
    catch (const std::exception& e) {
        emit registrationError(QString("Registration exception: %1").arg(e.what()));
    }
}

void RegistrationWorker::stopRegistration()
{
    m_stopRequested = true;
}

bool RegistrationWorker::performCorrelationRegistration()
{
    // Simplified registration implementation
    // In a real application, you would use ITK or more sophisticated algorithms
    
    vtkSmartPointer<vtkTransform> transform = vtkSmartPointer<vtkTransform>::New();
    transform->Identity();
    
    // Simulate registration progress
    for (int i = 10; i <= 90 && !m_stopRequested; i += 10) {
        QThread::msleep(200); // Simulate computation time
        emit progressUpdated(i);
    }
    
    emit transformUpdated(transform);
    return true;
}

#include "registrationmanager.moc"
